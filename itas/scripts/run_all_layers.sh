#!/bin/bash

# Multi-GPU Layer Processing Script
# Runs steered_inference.py create_activations for all layers across specified GPUs

# Configuration - EDIT THIS TO SPECIFY WHICH GPUs TO USE
AVAILABLE_GPUS=(4 5 6 7)  # Specify which GPUs to use
TOTAL_LAYERS=32
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
SAE_PATH="fnlp/Llama3_1-8B-Base-LXR-32x"
DATA_DIR="/data_x/junkim100/projects/scheming_sae/dataset_creation/scheming_dataset_llama-3.1-8B-Instruct.jsonl"
RESULTS_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/activations"

NUM_GPUS=${#AVAILABLE_GPUS[@]}

# Simple approach: launch all layers in batches
declare -a batch_pids=()

# Function to run a single layer
run_single_layer() {
    local layer=$1
    local gpu_id=$2

    echo "Starting layer $layer on GPU $gpu_id"

    CUDA_VISIBLE_DEVICES=$gpu_id python steered_inference.py create_activations \
        --model_name "$MODEL_NAME" \
        --sae_path "$SAE_PATH" \
        --data_source "$DATA_DIR" \
        --target_layer $layer &

    local pid=$!
    echo "Layer $layer started on GPU $gpu_id with PID $pid"
    return $pid
}

# Function to wait for all processes in current batch
wait_for_batch() {
    echo "Waiting for current batch to complete..."
    for pid in "${batch_pids[@]}"; do
        if kill -0 "$pid" 2>/dev/null; then
            wait "$pid"
            local exit_code=$?
            if [[ $exit_code -eq 0 ]]; then
                echo "Process $pid completed successfully"
            else
                echo "Process $pid failed with exit code $exit_code"
            fi
        fi
    done
    batch_pids=()  # Clear the batch
}



# Main execution
echo "Starting multi-GPU layer processing..."
echo "Total layers: $TOTAL_LAYERS"
echo "Available GPUs: ${AVAILABLE_GPUS[*]} (${NUM_GPUS} total)"
echo "Model: $MODEL_NAME"
echo "SAE Path: $SAE_PATH"
echo "Data Dir: $DATA_DIR"
echo "Results Dir: $RESULTS_DIR"
echo ""

# Create results directory
mkdir -p "$RESULTS_DIR"

# Process layers in batches equal to the number of GPUs
layer=0
batch_num=1

while [[ $layer -lt $TOTAL_LAYERS ]]; do
    echo ""
    echo "=== Starting Batch $batch_num ==="

    # Start a batch of layers (one per GPU)
    for ((i=0; i<NUM_GPUS && layer<TOTAL_LAYERS; i++)); do
        gpu_id=${AVAILABLE_GPUS[i]}

        # Start the layer processing
        run_single_layer $layer $gpu_id
        batch_pids+=($!)

        layer=$((layer + 1))

        # Small delay between starting processes
        sleep 1
    done

    echo "Batch $batch_num: Started layers $((layer - ${#batch_pids[@]})) to $((layer - 1))"

    # Wait for all processes in this batch to complete
    wait_for_batch

    echo "Batch $batch_num completed!"
    batch_num=$((batch_num + 1))
done

echo ""
echo "All layer processing completed!"
echo "Results saved in: $RESULTS_DIR"
